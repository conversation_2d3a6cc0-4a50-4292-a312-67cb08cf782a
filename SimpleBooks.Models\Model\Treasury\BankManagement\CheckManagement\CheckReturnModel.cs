﻿namespace SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement
{
    [Table("CheckReturn")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<CheckReturnModel>))]
    public partial class CheckReturnModel : BaseModel
    {
        [CustomRequired]
        [DisplayName("Return Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime ReturnDate { get; set; }
        [DisplayName("Refrance Number")]
        public string? RefranceNumber { get; set; }

        [CustomRequired]
        [DisplayName("Check Vault")]
        public Ulid CheckVaultId { get; set; }
        public virtual CheckVaultModel? CheckVault { get; set; }

        [CustomRequired]
        [DisplayName("Check Vault Location")]
        public Ulid CheckVaultLocationId { get; set; }
        public virtual CheckVaultLocationModel? CheckVaultLocation { get; set; }

        [CustomRequired]
        [DisplayName("Check Treasury Vouchers")]
        public virtual ICollection<CheckTreasuryVoucherModel> CheckTreasuryVouchers { get; set; } = new List<CheckTreasuryVoucherModel>();
        [DisplayName("Check Status Histories")]
        public virtual ICollection<CheckStatusHistoryModel> CheckStatusHistories { get; set; } = new List<CheckStatusHistoryModel>();
    }
}
