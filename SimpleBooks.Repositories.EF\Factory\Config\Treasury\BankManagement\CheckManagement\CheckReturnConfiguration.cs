﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Treasury.BankManagement.CheckManagement
{
    public class CheckReturnConfiguration : IEntityTypeConfiguration<CheckReturnModel>
    {
        public void Configure(EntityTypeBuilder<CheckReturnModel> builder)
        {
            builder.<PERSON><PERSON>ey(x => new { x.Id });

            builder.HasOne(d => d.CheckVault).WithMany(p => p.CheckReturns)
                .HasForeignKey(d => d.CheckVaultId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.CheckVaultLocation).WithMany(p => p.CheckReturns)
                .HasForeignKey(d => d.CheckVaultLocationId)
                .OnDelete(DeleteBehavior.ClientCascade);
        }
    }
}
