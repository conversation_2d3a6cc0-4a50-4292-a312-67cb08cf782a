﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SimpleBooks.Repositories.EF.Migrations.MySQLMigrations
{
    /// <inheritdoc />
    public partial class SimpleBooks_011 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CheckStatusHistory_CheckVaultLocation_CheckVaultLocationMode~",
                table: "CheckStatusHistory");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckStatusHistory_CheckVault_CheckVaultModelId",
                table: "CheckStatusHistory");

            migrationBuilder.DropIndex(
                name: "IX_CheckStatusHistory_CheckVaultLocationModelId",
                table: "CheckStatusHistory");

            migrationBuilder.DropIndex(
                name: "IX_CheckStatusHistory_CheckVaultModelId",
                table: "CheckStatusHistory");

            migrationBuilder.DropColumn(
                name: "CheckVaultLocationModelId",
                table: "CheckStatusHistory");

            migrationBuilder.DropColumn(
                name: "CheckVaultModelId",
                table: "CheckStatusHistory");

            migrationBuilder.AddColumn<byte[]>(
                name: "CheckVaultId",
                table: "CheckReject",
                type: "varbinary(16)",
                nullable: false,
                defaultValue: new byte[0]);

            migrationBuilder.AddColumn<byte[]>(
                name: "CheckVaultLocationId",
                table: "CheckReject",
                type: "varbinary(16)",
                nullable: false,
                defaultValue: new byte[0]);

            migrationBuilder.CreateIndex(
                name: "IX_CheckReject_CheckVaultId",
                table: "CheckReject",
                column: "CheckVaultId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckReject_CheckVaultLocationId",
                table: "CheckReject",
                column: "CheckVaultLocationId");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckReject_CheckVaultLocation_CheckVaultLocationId",
                table: "CheckReject",
                column: "CheckVaultLocationId",
                principalTable: "CheckVaultLocation",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckReject_CheckVault_CheckVaultId",
                table: "CheckReject",
                column: "CheckVaultId",
                principalTable: "CheckVault",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CheckReject_CheckVaultLocation_CheckVaultLocationId",
                table: "CheckReject");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckReject_CheckVault_CheckVaultId",
                table: "CheckReject");

            migrationBuilder.DropIndex(
                name: "IX_CheckReject_CheckVaultId",
                table: "CheckReject");

            migrationBuilder.DropIndex(
                name: "IX_CheckReject_CheckVaultLocationId",
                table: "CheckReject");

            migrationBuilder.DropColumn(
                name: "CheckVaultId",
                table: "CheckReject");

            migrationBuilder.DropColumn(
                name: "CheckVaultLocationId",
                table: "CheckReject");

            migrationBuilder.AddColumn<byte[]>(
                name: "CheckVaultLocationModelId",
                table: "CheckStatusHistory",
                type: "varbinary(16)",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "CheckVaultModelId",
                table: "CheckStatusHistory",
                type: "varbinary(16)",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_CheckStatusHistory_CheckVaultLocationModelId",
                table: "CheckStatusHistory",
                column: "CheckVaultLocationModelId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckStatusHistory_CheckVaultModelId",
                table: "CheckStatusHistory",
                column: "CheckVaultModelId");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckStatusHistory_CheckVaultLocation_CheckVaultLocationMode~",
                table: "CheckStatusHistory",
                column: "CheckVaultLocationModelId",
                principalTable: "CheckVaultLocation",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckStatusHistory_CheckVault_CheckVaultModelId",
                table: "CheckStatusHistory",
                column: "CheckVaultModelId",
                principalTable: "CheckVault",
                principalColumn: "Id");
        }
    }
}
