﻿namespace SimpleBooks.WEB.Controllers.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckReturnController : BaseBusinessController<
        CheckReturnModel,
        CheckReturnModel,
        CreateCheckReturnViewModel,
        UpdateCheckReturnViewModel,
        IndexCheckReturnFormViewModel,
        CreateCheckReturnFormViewModel,
        UpdateCheckReturnFormViewModel>
    {
        private readonly ICheckReturnService _checkReturnService;

        public CheckReturnController(ICheckReturnService checkReturnService) : base(checkReturnService)
        {
            _checkReturnService = checkReturnService;
        }

        [FixCheckReturn]
        public override async Task<IActionResult> Create(CreateCheckReturnFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            try
            {
                var result = await _checkReturnService.AddAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("checkReturn", ex.Message);
                return await FullFillViewAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            CheckReturnModel? entity = await _checkReturnService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            JsonSerializerSettings jsonSerializerSettings = new JsonSerializerSettings()
            {
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            };
            UpdateCheckReturnFormViewModel viewModel = new UpdateCheckReturnFormViewModel()
            {
                Id = entity.Id,
                ReturnDate = entity.ReturnDate,
                RefranceNumber = entity.RefranceNumber,
                CheckVaultId = entity.CheckVaultId,
                CheckVaultLocationId = entity.CheckVaultLocationId,
                SelectedCheckTreasuryVouchers = entity.CheckTreasuryVouchers.Select(x => x.Id).ToList(),
                AllCheckTreasuryVouchers = await _checkReturnService.SelectiveCheckTreasuryVouchers().GetDataOrThrowIfNullAsync(),
                CheckVaults = await _checkReturnService.SelectiveCheckVaultListAsync().ToSelectListItemAsync(),
                CheckVaultLocations = await _checkReturnService.SelectiveCheckVaultLocationListAsync().GetDataOrThrowIfNullAsync(),
                SelectedChecksJson = JsonConvert.SerializeObject(entity.CheckTreasuryVouchers, jsonSerializerSettings),
            };

            return View(viewModel);
        }

        [FixCheckReturn]
        public override async Task<IActionResult> Update(UpdateCheckReturnFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            try
            {
                var result = await _checkReturnService.UpdateAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("checkReturn", ex.Message);
                return await FailedActionResultAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreateCheckReturnFormViewModel model)
        {
            model.AllCheckTreasuryVouchers = await _checkReturnService.SelectiveCheckTreasuryVouchers().GetDataOrThrowIfNullAsync();
            model.CheckVaults = await _checkReturnService.SelectiveCheckVaultListAsync().ToSelectListItemAsync();
            model.CheckVaultLocations = await _checkReturnService.SelectiveCheckVaultLocationListAsync().GetDataOrThrowIfNullAsync();
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdateCheckReturnFormViewModel model)
        {
            model.AllCheckTreasuryVouchers = await _checkReturnService.SelectiveCheckTreasuryVouchers().GetDataOrThrowIfNullAsync();
            model.CheckVaults = await _checkReturnService.SelectiveCheckVaultListAsync().ToSelectListItemAsync();
            model.CheckVaultLocations = await _checkReturnService.SelectiveCheckVaultLocationListAsync().GetDataOrThrowIfNullAsync();
            return await Task.FromResult(View(model));
        }
    }
}
