﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SimpleBooks.Repositories.EF.Migrations.MySQLMigrations
{
    /// <inheritdoc />
    public partial class SimpleBooks_012 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CheckReject_CheckVaultLocation_CheckVaultLocationId",
                table: "CheckReject");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckReject_CheckVault_CheckVaultId",
                table: "CheckReject");

            migrationBuilder.DropIndex(
                name: "IX_CheckReject_CheckVaultId",
                table: "CheckReject");

            migrationBuilder.DropIndex(
                name: "IX_CheckReject_CheckVaultLocationId",
                table: "CheckReject");

            migrationBuilder.DropColumn(
                name: "CheckVaultId",
                table: "CheckReject");

            migrationBuilder.DropColumn(
                name: "CheckVaultLocationId",
                table: "CheckReject");

            migrationBuilder.AddColumn<byte[]>(
                name: "CheckReturnId",
                table: "CheckTreasuryVoucher",
                type: "varbinary(16)",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "CheckReturnId",
                table: "CheckStatusHistory",
                type: "varbinary(16)",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "CheckReturn",
                columns: table => new
                {
                    Id = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    ReturnDate = table.Column<DateTime>(type: "Date", nullable: false),
                    RefranceNumber = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CheckVaultId = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    CheckVaultLocationId = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    CreatedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "tinyint(1)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CheckReturn", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CheckReturn_CheckVaultLocation_CheckVaultLocationId",
                        column: x => x.CheckVaultLocationId,
                        principalTable: "CheckVaultLocation",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckReturn_CheckVault_CheckVaultId",
                        column: x => x.CheckVaultId,
                        principalTable: "CheckVault",
                        principalColumn: "Id");
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_CheckReturnId",
                table: "CheckTreasuryVoucher",
                column: "CheckReturnId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckStatusHistory_CheckReturnId",
                table: "CheckStatusHistory",
                column: "CheckReturnId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckReturn_CheckVaultId",
                table: "CheckReturn",
                column: "CheckVaultId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckReturn_CheckVaultLocationId",
                table: "CheckReturn",
                column: "CheckVaultLocationId");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckStatusHistory_CheckReturn_CheckReturnId",
                table: "CheckStatusHistory",
                column: "CheckReturnId",
                principalTable: "CheckReturn",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckTreasuryVoucher_CheckReturn_CheckReturnId",
                table: "CheckTreasuryVoucher",
                column: "CheckReturnId",
                principalTable: "CheckReturn",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CheckStatusHistory_CheckReturn_CheckReturnId",
                table: "CheckStatusHistory");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckTreasuryVoucher_CheckReturn_CheckReturnId",
                table: "CheckTreasuryVoucher");

            migrationBuilder.DropTable(
                name: "CheckReturn");

            migrationBuilder.DropIndex(
                name: "IX_CheckTreasuryVoucher_CheckReturnId",
                table: "CheckTreasuryVoucher");

            migrationBuilder.DropIndex(
                name: "IX_CheckStatusHistory_CheckReturnId",
                table: "CheckStatusHistory");

            migrationBuilder.DropColumn(
                name: "CheckReturnId",
                table: "CheckTreasuryVoucher");

            migrationBuilder.DropColumn(
                name: "CheckReturnId",
                table: "CheckStatusHistory");

            migrationBuilder.AddColumn<byte[]>(
                name: "CheckVaultId",
                table: "CheckReject",
                type: "varbinary(16)",
                nullable: false,
                defaultValue: new byte[0]);

            migrationBuilder.AddColumn<byte[]>(
                name: "CheckVaultLocationId",
                table: "CheckReject",
                type: "varbinary(16)",
                nullable: false,
                defaultValue: new byte[0]);

            migrationBuilder.CreateIndex(
                name: "IX_CheckReject_CheckVaultId",
                table: "CheckReject",
                column: "CheckVaultId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckReject_CheckVaultLocationId",
                table: "CheckReject",
                column: "CheckVaultLocationId");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckReject_CheckVaultLocation_CheckVaultLocationId",
                table: "CheckReject",
                column: "CheckVaultLocationId",
                principalTable: "CheckVaultLocation",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckReject_CheckVault_CheckVaultId",
                table: "CheckReject",
                column: "CheckVaultId",
                principalTable: "CheckVault",
                principalColumn: "Id");
        }
    }
}
