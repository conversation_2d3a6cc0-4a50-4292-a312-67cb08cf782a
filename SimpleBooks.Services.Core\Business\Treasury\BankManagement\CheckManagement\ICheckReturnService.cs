﻿namespace SimpleBooks.Services.Core.Business.Treasury.BankManagement.CheckManagement
{
    public interface ICheckReturnService : ISimpleBooksBaseService<CheckReturnModel, CheckReturnModel, CreateCheckReturnViewModel, UpdateCheckReturnViewModel>
    {
        Task<ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>> SelectiveCheckTreasuryVouchers();
        Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCheckVaultListAsync();
        Task<ServiceResult<IEnumerable<CheckVaultLocationModel>>> SelectiveCheckVaultLocationListAsync();
    }
}
