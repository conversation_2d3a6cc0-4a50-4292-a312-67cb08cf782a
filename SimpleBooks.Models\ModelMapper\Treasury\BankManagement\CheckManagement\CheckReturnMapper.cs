﻿namespace SimpleBooks.Models.ModelMapper.Treasury.BankManagement.CheckManagement
{
    public static class CheckReturnMapper
    {
        public static CreateCheckReturnViewModel ToCreateDto(this CheckReturnModel entity)
        {
            CreateCheckReturnViewModel viewModel = new CreateCheckReturnViewModel()
            {
                ReturnDate = entity.ReturnDate,
                RefranceNumber = entity.RefranceNumber,
                CheckVaultId = entity.CheckVaultId,
                CheckVaultLocationId = entity.CheckVaultLocationId,
                SelectedCheckTreasuryVouchers = entity.CheckTreasuryVouchers.Select(x => x.Id).ToList(),
            };
            return viewModel;
        }

        public static CheckReturnModel ToEntity(this CreateCheckReturnViewModel entity)
        {
            CheckReturnModel model = new CheckReturnModel()
            {
                ReturnDate = entity.ReturnDate,
                RefranceNumber = entity.RefranceNumber,
                CheckVaultId = entity.CheckVaultId,
                CheckVaultLocationId = entity.CheckVaultLocationId,
                CheckTreasuryVouchers = entity.SelectedCheckTreasuryVouchers.Select(x => new CheckTreasuryVoucherModel()
                {
                    Id = x,
                }).ToList(),
            };
            return model;
        }

        public static UpdateCheckReturnViewModel ToUpdateDto(this CheckReturnModel entity)
        {
            UpdateCheckReturnViewModel viewModel = new UpdateCheckReturnViewModel()
            {
                Id = entity.Id,
                ReturnDate = entity.ReturnDate,
                RefranceNumber = entity.RefranceNumber,
                CheckVaultId = entity.CheckVaultId,
                CheckVaultLocationId = entity.CheckVaultLocationId,
                SelectedCheckTreasuryVouchers = entity.CheckTreasuryVouchers.Select(x => x.Id).ToList(),
            };
            return viewModel;
        }

        public static CheckReturnModel ToEntity(this UpdateCheckReturnViewModel entity)
        {
            CheckReturnModel model = new CheckReturnModel()
            {
                Id = entity.Id,
                ReturnDate = entity.ReturnDate,
                RefranceNumber = entity.RefranceNumber,
                CheckVaultId = entity.CheckVaultId,
                CheckVaultLocationId = entity.CheckVaultLocationId,
                CheckTreasuryVouchers = entity.SelectedCheckTreasuryVouchers.Select(x => new CheckTreasuryVoucherModel()
                {
                    Id = x,
                }).ToList(),
            };
            return model;
        }
    }
}
