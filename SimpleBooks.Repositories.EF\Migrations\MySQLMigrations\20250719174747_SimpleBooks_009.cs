﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SimpleBooks.Repositories.EF.Migrations.MySQLMigrations
{
    /// <inheritdoc />
    public partial class SimpleBooks_009 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CheckCollection_CheckDeposit_CheckDepositId",
                table: "CheckCollection");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckCollection_CheckTreasuryVoucher_CheckTreasuryVoucherId",
                table: "CheckCollection");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckReject_CheckDeposit_CheckDepositId",
                table: "CheckReject");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckReject_CheckTreasuryVoucher_CheckTreasuryVoucherId",
                table: "CheckReject");

            migrationBuilder.DropIndex(
                name: "IX_CheckReject_CheckDepositId",
                table: "CheckReject");

            migrationBuilder.DropIndex(
                name: "IX_CheckReject_CheckTreasuryVoucherId",
                table: "CheckReject");

            migrationBuilder.DropIndex(
                name: "IX_CheckCollection_CheckDepositId",
                table: "CheckCollection");

            migrationBuilder.DropIndex(
                name: "IX_CheckCollection_CheckTreasuryVoucherId",
                table: "CheckCollection");

            migrationBuilder.DropColumn(
                name: "CheckDepositId",
                table: "CheckReject");

            migrationBuilder.DropColumn(
                name: "CheckTreasuryVoucherId",
                table: "CheckReject");

            migrationBuilder.DropColumn(
                name: "CheckDepositId",
                table: "CheckCollection");

            migrationBuilder.DropColumn(
                name: "CheckTreasuryVoucherId",
                table: "CheckCollection");

            migrationBuilder.AddColumn<byte[]>(
                name: "CheckCollectionId",
                table: "CheckTreasuryVoucher",
                type: "varbinary(16)",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "CheckRejectId",
                table: "CheckTreasuryVoucher",
                type: "varbinary(16)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RejectReason",
                table: "CheckReject",
                type: "longtext",
                nullable: false)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_CheckCollectionId",
                table: "CheckTreasuryVoucher",
                column: "CheckCollectionId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_CheckRejectId",
                table: "CheckTreasuryVoucher",
                column: "CheckRejectId");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckTreasuryVoucher_CheckCollection_CheckCollectionId",
                table: "CheckTreasuryVoucher",
                column: "CheckCollectionId",
                principalTable: "CheckCollection",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckTreasuryVoucher_CheckReject_CheckRejectId",
                table: "CheckTreasuryVoucher",
                column: "CheckRejectId",
                principalTable: "CheckReject",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CheckTreasuryVoucher_CheckCollection_CheckCollectionId",
                table: "CheckTreasuryVoucher");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckTreasuryVoucher_CheckReject_CheckRejectId",
                table: "CheckTreasuryVoucher");

            migrationBuilder.DropIndex(
                name: "IX_CheckTreasuryVoucher_CheckCollectionId",
                table: "CheckTreasuryVoucher");

            migrationBuilder.DropIndex(
                name: "IX_CheckTreasuryVoucher_CheckRejectId",
                table: "CheckTreasuryVoucher");

            migrationBuilder.DropColumn(
                name: "CheckCollectionId",
                table: "CheckTreasuryVoucher");

            migrationBuilder.DropColumn(
                name: "CheckRejectId",
                table: "CheckTreasuryVoucher");

            migrationBuilder.DropColumn(
                name: "RejectReason",
                table: "CheckReject");

            migrationBuilder.AddColumn<byte[]>(
                name: "CheckDepositId",
                table: "CheckReject",
                type: "varbinary(16)",
                nullable: false,
                defaultValue: new byte[0]);

            migrationBuilder.AddColumn<byte[]>(
                name: "CheckTreasuryVoucherId",
                table: "CheckReject",
                type: "varbinary(16)",
                nullable: false,
                defaultValue: new byte[0]);

            migrationBuilder.AddColumn<byte[]>(
                name: "CheckDepositId",
                table: "CheckCollection",
                type: "varbinary(16)",
                nullable: false,
                defaultValue: new byte[0]);

            migrationBuilder.AddColumn<byte[]>(
                name: "CheckTreasuryVoucherId",
                table: "CheckCollection",
                type: "varbinary(16)",
                nullable: false,
                defaultValue: new byte[0]);

            migrationBuilder.CreateIndex(
                name: "IX_CheckReject_CheckDepositId",
                table: "CheckReject",
                column: "CheckDepositId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckReject_CheckTreasuryVoucherId",
                table: "CheckReject",
                column: "CheckTreasuryVoucherId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckCollection_CheckDepositId",
                table: "CheckCollection",
                column: "CheckDepositId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckCollection_CheckTreasuryVoucherId",
                table: "CheckCollection",
                column: "CheckTreasuryVoucherId");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckCollection_CheckDeposit_CheckDepositId",
                table: "CheckCollection",
                column: "CheckDepositId",
                principalTable: "CheckDeposit",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckCollection_CheckTreasuryVoucher_CheckTreasuryVoucherId",
                table: "CheckCollection",
                column: "CheckTreasuryVoucherId",
                principalTable: "CheckTreasuryVoucher",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckReject_CheckDeposit_CheckDepositId",
                table: "CheckReject",
                column: "CheckDepositId",
                principalTable: "CheckDeposit",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckReject_CheckTreasuryVoucher_CheckTreasuryVoucherId",
                table: "CheckReject",
                column: "CheckTreasuryVoucherId",
                principalTable: "CheckTreasuryVoucher",
                principalColumn: "Id");
        }
    }
}
