﻿namespace SimpleBooks.Services.API.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckReturnService : SimpleBooksBaseService<CheckReturnModel, CheckReturnModel, CreateCheckReturnViewModel, UpdateCheckReturnViewModel>, ICheckReturnService
    {
        private readonly ICheckTreasuryVoucherService _checkTreasuryVoucherService;
        private readonly ICheckVaultService _checkVaultService;
        private readonly ICheckVaultLocationService _checkVaultLocationService;

        public CheckReturnService(
            IAuthenticationValidationService authenticationValidationService,
            IHttpClientFactory httpClientFactory,
            ICheckTreasuryVoucherService checkTreasuryVoucherService,
            ICheckVaultService checkVaultService,
            ICheckVaultLocationService checkVaultLocationService) : base(authenticationValidationService, httpClientFactory)
        {
            _checkTreasuryVoucherService = checkTreasuryVoucherService;
            _checkVaultService = checkVaultService;
            _checkVaultLocationService = checkVaultLocationService;
        }

        public async Task<ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>> SelectiveCheckTreasuryVouchers() => await _checkTreasuryVoucherService.SelectiveCheckTreasuryVouchersAsync(CheckStatusEnumeration.Rejected.Value);
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCheckVaultListAsync() => await _checkVaultService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<CheckVaultLocationModel>>> SelectiveCheckVaultLocationListAsync() => await _checkVaultLocationService.GetAllAsync();
    }
}
