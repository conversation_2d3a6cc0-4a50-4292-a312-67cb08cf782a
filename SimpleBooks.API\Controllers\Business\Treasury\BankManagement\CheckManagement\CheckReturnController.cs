﻿namespace SimpleBooks.API.Controllers.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckReturnController : BaseBusinessController<CheckReturnModel, CheckReturnModel, CreateCheckReturnViewModel, UpdateCheckReturnViewModel>
    {
        private readonly ICheckReturnService _checkReturnService;

        public CheckReturnController(ICheckReturnService checkReturnService) : base(checkReturnService)
        {
            _checkReturnService = checkReturnService;
        }
    }
}
