﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SimpleBooks.Repositories.EF.Migrations.MySQLMigrations
{
    /// <inheritdoc />
    public partial class SimpleBooks_010 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "CheckStatus",
                columns: new[] { "Id", "CheckStatusName", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "IsActive", "ModifaiedAt", "ModifaiedBy" },
                values: new object[] { new byte[] { 1, 152, 36, 25, 206, 161, 247, 233, 47, 93, 6, 121, 216, 73, 83, 36 }, "Collected", null, null, null, null, true, null, null });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "CheckStatus",
                keyColumn: "Id",
                keyValue: new byte[] { 1, 152, 36, 25, 206, 161, 247, 233, 47, 93, 6, 121, 216, 73, 83, 36 });
        }
    }
}
