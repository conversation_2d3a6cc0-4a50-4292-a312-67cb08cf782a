﻿namespace SimpleBooks.WEB.ViewModels.Treasury.BankManagement.CheckManagement.CheckReturn
{
    public class UpdateCheckReturnFormViewModel : UpdateCheckReturnViewModel
    {
        public IEnumerable<CheckTreasuryVoucherModel> AllCheckTreasuryVouchers { get; set; } = Enumerable.Empty<CheckTreasuryVoucherModel>();
        public IEnumerable<SelectListItem> CheckVaults { get; set; } = Enumerable.Empty<SelectListItem>();
        public IEnumerable<CheckVaultLocationModel> CheckVaultLocations { get; set; } = Enumerable.Empty<CheckVaultLocationModel>();
        public string SelectedChecksJson { get; set; } = string.Empty;
    }
}
