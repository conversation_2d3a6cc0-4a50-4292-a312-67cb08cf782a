﻿import { treasuryLineTools } from '/js/business/Treasury/treasuryLineTools.js';

export class CheckReturnHandler {
    constructor(checkVaults, checkVaultLocations) {
        this.checkVaults = checkVaults;
        this.checkVaultLocations = checkVaultLocations;
    }

    onCheckVaultChanged(checkVaultId) {
        const checkVaultLocationIdSelect = document.getElementById('CheckVaultLocationId')
        if (!checkVaultLocationIdSelect) return;

        const checkVaultLocations = this.checkVaultLocations.$values.filter(x => x.CheckVaultId == checkVaultId)

        checkVaultLocationIdSelect.innerHTML = '<option value=""></option>';
        checkVaultLocations.forEach(checkVaultLocation => {
            const opt = document.createElement("option");
            opt.value = checkVaultLocation.Id;
            opt.text = checkVaultLocation.CheckVaultLocationCurrency + ' - ' + checkVaultLocation.CheckVaultLocationNumber;
            checkVaultLocationIdSelect.appendChild(opt);
        });
    }
}
