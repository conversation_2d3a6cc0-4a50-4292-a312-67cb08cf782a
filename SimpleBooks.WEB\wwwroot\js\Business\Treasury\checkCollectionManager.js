/**
 * Check Collection functionality for managing check selection and form submission
 * This module handles the UI interactions for moving checks between available and selected lists
 */

export class CheckCollectionManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSelectedChecks();
    }

    /**
     * Load previously selected checks from the hidden input field
     */
    loadSelectedChecks() {
        const selectedChecksJson = document.getElementById("SelectedChecksJson")?.value;
        let selectedItems = [];

        try {
            selectedItems = JSON.parse(selectedChecksJson || '[]');
        } catch (e) {
            console.error("Invalid JSON for selected checks:", e);
        }

        const selectedList = document.getElementById("SelectedCheckes");
        if (!selectedList) return;

        selectedItems.forEach(item => {
            const li = this.createCheckListItem(item, 'selected');
            selectedList.appendChild(li);
        });
    }

    /**
     * Create a list item element for a check
     * @param {Object} item - The check data object
     * @param {string} type - 'available' or 'selected'
     * @returns {HTMLElement} The created list item element
     */
    createCheckListItem(item, type) {
        const li = document.createElement("li");
        li.className = "list-group-item d-flex justify-content-between align-items-start";
        
        // Store the full object for future access
        li.setAttribute("data-item", JSON.stringify(item));

        const buttonText = type === 'selected' ? '←' : '→';
        const buttonClass = type === 'selected' ? 'move-left' : 'move-right';

        li.innerHTML = `
            <div class="me-auto">
                <div class="fw-bold">${item.CheckNumber}</div>
                <div>Issuer Name: <span class="text-success">${item.IssuerName}</span></div>
                <div>Due Date: <span class="text-success">${new Date(item.DueDate).toLocaleDateString()}</span></div>
                <div>Amount: <span class="text-success">${parseFloat(item.Amount).toLocaleString()}</span></div>
            </div>
            <button type="button" class="btn btn-sm btn-outline-primary ${buttonClass}">${buttonText}</button>
        `;

        return li;
    }

    /**
     * Setup all event listeners for the check collection functionality
     */
    setupEventListeners() {
        // Individual item move buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('move-right')) {
                const li = e.target.closest('li');
                this.moveItem(li, 'SelectedCheckes', '←', 'move-left');
            } else if (e.target.classList.contains('move-left')) {
                const li = e.target.closest('li');
                this.moveItem(li, 'AvailableCheckes', '→', 'move-right');
            }
        });

        // Move all buttons
        const moveAllRightBtn = document.getElementById('moveAllRight');
        if (moveAllRightBtn) {
            moveAllRightBtn.addEventListener('click', () => {
                this.moveAllItems('AvailableCheckes', 'SelectedCheckes', '←', 'move-left');
            });
        }

        const moveAllLeftBtn = document.getElementById('moveAllLeft');
        if (moveAllLeftBtn) {
            moveAllLeftBtn.addEventListener('click', () => {
                this.moveAllItems('SelectedCheckes', 'AvailableCheckes', '→', 'move-right');
            });
        }

        // Form submission
        const form = document.getElementById('myForm');
        if (form) {
            form.addEventListener('submit', (e) => {
                this.handleFormSubmit(e);
            });
        }
    }

    /**
     * Move a single item between lists
     * @param {HTMLElement} item - The list item to move
     * @param {string} targetListId - ID of the target list
     * @param {string} buttonText - Text for the button
     * @param {string} buttonClass - CSS class for the button
     */
    moveItem(item, targetListId, buttonText, buttonClass) {
        const newItem = item.cloneNode(true); // Keep structure intact
        const btn = newItem.querySelector('button');
        btn.textContent = buttonText;
        btn.className = `btn btn-sm btn-outline-primary ${buttonClass}`;
        btn.type = 'button'; // Ensure it's not treated as submit
        
        const targetList = document.getElementById(targetListId);
        if (targetList) {
            targetList.appendChild(newItem);
        }
        item.remove();
    }

    /**
     * Move all items between lists
     * @param {string} fromListId - ID of the source list
     * @param {string} toListId - ID of the target list
     * @param {string} buttonText - Text for the buttons
     * @param {string} buttonClass - CSS class for the buttons
     */
    moveAllItems(fromListId, toListId, buttonText, buttonClass) {
        const fromList = document.getElementById(fromListId);
        const toList = document.getElementById(toListId);

        if (!fromList || !toList) return;

        // Convert HTMLCollection to Array to avoid live collection issues
        const items = Array.from(fromList.children);

        items.forEach(item => {
            // Clone full item including nested elements
            const newItem = item.cloneNode(true);

            // Update the button inside the item
            const button = newItem.querySelector('button');
            if (button) {
                button.textContent = buttonText;
                button.className = `btn btn-sm btn-outline-primary ${buttonClass}`;
                button.type = 'button'; // Ensure it's not treated as submit
            }

            toList.appendChild(newItem);
            item.remove(); // Remove from original list
        });
    }

    /**
     * Handle form submission by updating the hidden input with selected checks
     * @param {Event} e - The form submit event
     */
    handleFormSubmit(e) {
        const selectedItems = document.querySelectorAll('#SelectedCheckes li');
        const selectedData = Array.from(selectedItems).map(item => {
            try {
                return JSON.parse(item.dataset.item);
            } catch (error) {
                console.warn('Invalid JSON in data-item', item);
                return null;
            }
        }).filter(x => x !== null);

        const hiddenInput = document.getElementById('SelectedChecksJson');
        if (hiddenInput) {
            hiddenInput.value = JSON.stringify(selectedData);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
    window.checkCollectionManager = new CheckCollectionManager();
});
