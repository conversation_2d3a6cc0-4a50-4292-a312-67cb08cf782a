﻿namespace SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckReturn
{
    public class CreateCheckReturnViewModel : BaseCreateViewModel, IEntityMapper<CheckReturnModel, CreateCheckReturnViewModel>
    {
        [CustomRequired]
        [DisplayName("Return Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime ReturnDate { get; set; }
        [DisplayName("Refrance Number")]
        public string? RefranceNumber { get; set; }
        [CustomRequired]
        [DisplayName("Check Vault")]
        public Ulid CheckVaultId { get; set; }
        [CustomRequired]
        [DisplayName("Check Vault Location")]
        public Ulid CheckVaultLocationId { get; set; }

        [CustomRequired]
        [DisplayName("Check Treasury Vouchers")]
        public virtual ICollection<Ulid> SelectedCheckTreasuryVouchers { get; set; } = new List<Ulid>();

        public CreateCheckReturnViewModel ToDto(CheckReturnModel entity) => entity.ToCreateDto();

        public CheckReturnModel ToEntity() => CheckReturnMapper.ToEntity(this);
    }
}
